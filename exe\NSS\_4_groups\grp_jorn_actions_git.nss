
//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')
$BATCH_FINISHED_MSG1 = "TITLE "+"["+sys.datetime("H.M")+"]"+" Finished ... Exiting"
$BATCH_FINISHED_MSG2 = "ECHO. & ECHO. & ECHO Finished && ECHO ---------"
$BATCH_FINISHED_MSG3 = "ECHO Window will close in 3 seconds ... & PING 127.0.0.1 -n 3 > NUL & EXIT"
$BATCH_EXIT_WITH_MSG = '@BATCH_FINISHED_MSG1 && @BATCH_FINISHED_MSG2 && @BATCH_FINISHED_MSG3'
//
$CMD_GIT_ADD_ALL = '/C (CD /D "@sel.dir") && (git add *)'
$CMD_GIT_ADD_ALL_V = '/K (CD /D "@sel.dir") && (git add *) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
$CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
// $CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (echo @BATCH_SEL_AS_STRING) && (git add  @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'

$CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
//
$CMD_GIT_ADD_ALL_F = '/C (CD /D "@sel.dir") && (git add * -f) && (@BATCH_EXIT_WITH_MSG)'
$CMD_GIT_ADD_ALL_F_V = '/K (CD /D "@sel.dir") && (git add * -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
// $CMD_GIT_ADD_SELECTION_F = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG)'
$CMD_GIT_ADD_SELECTION_F = '/C (CD /D \"@sel.dir\") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
$CMD_GIT_ADD_SELECTION_F_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
//
$CMD_GIT_UNSTAGE_ALL = '/C (CD /D "@sel.dir") && (git reset HEAD .)'
$CMD_GIT_UNSTAGE_ALL_V = '/K (CD /D "@sel.dir") && (git reset HEAD .) && PAUSE'
$CMD_GIT_UNSTAGE_SELECTION = '/C (CD /D "@sel.dir") && (git reset @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
$CMD_GIT_UNSTAGE_SELECTION_V = '/K (CD /D "@sel.dir") && (git reset @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
//
$CMD_GIT_COMMIT_ALL = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (git commit -m "chk" --no-gpg-sign) && PAUSE'
$CMD_GIT_COMMIT_ALL_V = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (git commit -m "chk" --no-gpg-sign) && (@BATCH_EXIT_WITH_MSG) && PAUSE'
//
item(title="Git Commands" image=[E22C,DARK] vis='Static' sep='Both')


// git commit all in folder
item(title='&git add . && git commit -m "chk"'
    keys="..."
    image=[E26E,RED]
    image-sel=[E26E,RED]
    type='Desktop|Drive|Back.Dir|Back.Drive|Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_COMMIT_ALL_V','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_COMMIT_ALL_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)

// git add
item(title="&git add: *"
    keys="..."
    image=[E26E,BLUE]
    image-sel=[E26E,GREEN]
    type='Desktop|Drive|Back.Dir|Back.Drive|Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_ADD_ALL','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_ADD_ALL_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)
item(title="&git add: (selection)"
    keys="..."
    image=[E26E,BLUE]
    image-sel=[E26E,GREEN]
    type='Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_ADD_SELECTION','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_ADD_SELECTION',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)
separator()

// git add --force
item(title="&git add: * -f"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='Desktop|Drive|Back.Dir|Back.Drive|Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_ADD_ALL_F','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_ADD_ALL_F',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)
item(title="&git add: (selection) -f"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_ADD_SELECTION_F','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_ADD_SELECTION_F',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)
separator()

// git unstage
item(title="&git unstage: *"
    keys="..."
    image=[E26E,DARK]
    image-sel=[E26E,HOVER]
    type='Desktop|Drive|Back.Dir|Back.Drive|Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_UNSTAGE_ALL','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_UNSTAGE_ALL',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_ALWAYS
)
item(title="&git unstage: (selection)"
    keys="..."
    image=[E26E,DARK]
    image-sel=[E26E,HOVER]
    type='Dir|File'
    where=sel.count>=1
    tip=['@CMD_GIT_UNSTAGE_SELECTION','@tip.info',0.75]
    admin=KEYS_EXE_ADMIN
    commands{
        cmd-line='@CMD_GIT_UNSTAGE_SELECTION',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_ALWAYS
)


